[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://bfut0jcvc0jl8"
path="res://.godot/imported/SourceCodePro-Italic-VariableFont_wght.ttf-d5a46208c7d015f2e310ca0c20b18623.fontdata"

[deps]

source_file="res://Source_Code_Pro/SourceCodePro-Italic-VariableFont_wght.ttf"
dest_files=["res://.godot/imported/SourceCodePro-Italic-VariableFont_wght.ttf-d5a46208c7d015f2e310ca0c20b18623.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
