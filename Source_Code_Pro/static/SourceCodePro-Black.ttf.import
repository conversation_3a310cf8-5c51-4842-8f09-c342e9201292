[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://dmrjjbuqo7b23"
path="res://.godot/imported/SourceCodePro-Black.ttf-17d94d1d61985cc27e2486969f5a3b26.fontdata"

[deps]

source_file="res://Source_Code_Pro/static/SourceCodePro-Black.ttf"
dest_files=["res://.godot/imported/SourceCodePro-Black.ttf-17d94d1d61985cc27e2486969f5a3b26.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
