[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://dc6o60vh3ewmr"
path="res://.godot/imported/SourceCodePro-BlackItalic.ttf-83f8fb2d729265f218388e9a161feecc.fontdata"

[deps]

source_file="res://Source_Code_Pro/static/SourceCodePro-BlackItalic.ttf"
dest_files=["res://.godot/imported/SourceCodePro-BlackItalic.ttf-83f8fb2d729265f218388e9a161feecc.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
