[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://boy27m0q6e8p1"
path="res://.godot/imported/SourceCodePro-Bold.ttf-1dd30c541e313030b4b60d9518381cea.fontdata"

[deps]

source_file="res://Source_Code_Pro/static/SourceCodePro-Bold.ttf"
dest_files=["res://.godot/imported/SourceCodePro-Bold.ttf-1dd30c541e313030b4b60d9518381cea.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
