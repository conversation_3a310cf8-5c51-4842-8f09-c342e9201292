[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://dqtsd7c5dvscr"
path="res://.godot/imported/SourceCodePro-ExtraBold.ttf-d4879ac83ec5279743fa2db0a72d655a.fontdata"

[deps]

source_file="res://Source_Code_Pro/static/SourceCodePro-ExtraBold.ttf"
dest_files=["res://.godot/imported/SourceCodePro-ExtraBold.ttf-d4879ac83ec5279743fa2db0a72d655a.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
