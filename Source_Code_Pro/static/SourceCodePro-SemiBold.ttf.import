[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://dwwro4t1ick4y"
path="res://.godot/imported/SourceCodePro-SemiBold.ttf-0d1c90900d1725619da0f6e0b50ef024.fontdata"

[deps]

source_file="res://Source_Code_Pro/static/SourceCodePro-SemiBold.ttf"
dest_files=["res://.godot/imported/SourceCodePro-SemiBold.ttf-0d1c90900d1725619da0f6e0b50ef024.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
