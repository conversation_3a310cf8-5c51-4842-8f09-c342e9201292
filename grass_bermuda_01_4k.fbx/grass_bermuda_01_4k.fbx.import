[remap]

importer="scene"
importer_version=1
type="PackedScene"
uid="uid://cmltsbxmcrs1i"
path="res://.godot/imported/grass_bermuda_01_4k.fbx-df9be2fbc03be6ee9f0c047476d574b0.scn"

[deps]

source_file="res://grass_bermuda_01_4k.fbx/grass_bermuda_01_4k.fbx"
dest_files=["res://.godot/imported/grass_bermuda_01_4k.fbx-df9be2fbc03be6ee9f0c047476d574b0.scn"]

[params]

nodes/root_type=""
nodes/root_name=""
nodes/apply_root_scale=true
nodes/root_scale=1.0
nodes/import_as_skeleton_bones=false
nodes/use_node_type_suffixes=true
meshes/ensure_tangents=true
meshes/generate_lods=true
meshes/create_shadow_meshes=true
meshes/light_baking=1
meshes/lightmap_texel_size=0.2
meshes/force_disable_compression=false
skins/use_named_skins=true
animation/import=true
animation/fps=30
animation/trimming=true
animation/remove_immutable_tracks=true
animation/import_rest_as_RESET=false
import_script/path=""
_subresources={}
fbx/importer=0
fbx/allow_geometry_helper_nodes=false
fbx/embedded_image_handling=1
